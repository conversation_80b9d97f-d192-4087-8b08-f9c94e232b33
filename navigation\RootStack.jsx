import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import Splash from "../screens/auth/Splash";
import GetStarted from "../screens/auth/GetStarted";
import Profile from "../screens/Profile";
import ProfileDetails from "../screens/ProfileDetails";
import Recommended from "../screens/Recommended";
import TabNavigator from "./TabNavigator";
import Settings from "../screens/Settings";
import ChangePassword from "../screens/ChangePassword";
import Checkout from "../screens/Checkout";
import PaymentMethods from "../screens/PaymentMethods";
import Referal from "../screens/Referal";
import RateUs from "../screens/RateUs";
import FAQ from "../screens/FAQ";
import VerifyNumber from "../screens/auth/VerifyNumber";
import Coupon from "../screens/Coupon";
import ReviewRestaurants from "../screens/ReviewRestaurant";
import YourOrders from "../screens/YourOrders";
import OrderStatus from "../screens/OrderStatus";
import Thankyou from "../screens/Thankyou";
import ListingNonVeg from "../screens/ListingNonVeg";
import ReviewsView from "../screens/ReviewsView";

import NoResultsFound from "../screens/NoResultsFound";
import NoLocationFound from "../screens/NoLocationFound";

import Cart from "../screens/Cart";
import Allergens from "../screens/Allergens";
import Screen1 from "../screens/onBoarding/Screen1";
import Screen3 from "../screens/onBoarding/Screen3";
import Screen2 from "../screens/onBoarding/Screen2";
import Onboarding from "../screens/onBoarding/OnBoarding";
import ContactUs from "../screens/ContactUs";

const Stack = createStackNavigator();

const RootStack = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Splash">
        <Stack.Screen
          name="Splash"
          component={Splash}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="GetStarted"
          component={GetStarted}
          options={{ headerShown: false, gestureEnabled: false }}
        />

        <Stack.Screen
          name="OnBoarding"
          component={Onboarding}
          options={{ headerShown: false, gestureEnabled: false }}
        />

        <Stack.Screen
          name="VerifyNumber"
          component={VerifyNumber}
          options={{ headerShown: false }}
        />

        <Stack.Screen
          name="Profile"
          component={Profile}
          options={{ headerShown: false, title: "Profile" }}
        />
        <Stack.Screen
          name="Profile_Details"
          component={ProfileDetails}
          options={{ headerShown: false, title: "Profile_Details" }}
        />
        <Stack.Screen
          name="Recommended"
          component={Recommended}
          options={{ headerShown: false, title: "Recommended" }}
        />

        <Stack.Screen
          name="Settings"
          component={Settings}
          options={{ headerShown: false, title: "Settings" }}
        />

        <Stack.Screen
          name="Checkout"
          component={Checkout}
          options={{ headerShown: false, title: "Checkout" }}
        />

        <Stack.Screen
          name="ChangePassword"
          component={ChangePassword}
          options={{ headerShown: false, title: "ChangePassword" }}
        />

        <Stack.Screen
          name="PaymentMethods"
          component={PaymentMethods}
          options={{ headerShown: false, title: "PaymentMethods" }}
        />

        <Stack.Screen
          name="Referal"
          component={Referal}
          options={{ headerShown: false, title: "Referal" }}
        />

        <Stack.Screen
          name="RateUs"
          component={RateUs}
          options={{ headerShown: false, title: "RateUs" }}
        />

        <Stack.Screen
          name="FAQ"
          component={FAQ}
          options={{ headerShown: false, title: "FAQ" }}
        />

        <Stack.Screen
          name="Cart"
          component={Cart}
          options={{ headerShown: false, title: "Cart" }}
        />

        <Stack.Screen
          name="ContactUs"
          component={ContactUs}
          options={{ headerShown: false, title: "Cart" }}
        />

        <Stack.Screen
          name="Coupon"
          component={Coupon}
          options={{ headerShown: false, title: "Coupon" }}
        />

        <Stack.Screen
          name="Allergens"
          component={Allergens}
          options={{ headerShown: false, title: "Allergens" }}
        />

        <Stack.Screen
          name="ReviewRestaurant"
          component={ReviewRestaurants}
          options={{ headerShown: false, title: "ReviewRestaurant" }}
        />
        <Stack.Screen
          name="YourOrders"
          component={YourOrders}
          options={{ headerShown: false, title: "YourOrders" }}
        />

        <Stack.Screen
          name="OrderStatus"
          component={OrderStatus}
          options={{ headerShown: false, title: "OrderStatus" }}
        />

        <Stack.Screen
          name="Thankyou"
          component={Thankyou}
          options={{ headerShown: false, title: "Thankyou" }}
        />

        <Stack.Screen
          name="ListingNonVeg"
          component={ListingNonVeg}
          options={{ headerShown: false, title: "ListingNonVeg" }}
        />

        <Stack.Screen
          name="Reviews"
          component={ReviewsView}
          options={{ headerShown: false, title: "Reviews" }}
        />

        <Stack.Screen
          name="Screen1"
          component={Screen3}
          options={{ headerShown: false, title: "Screen1" }}
        />

        <Stack.Screen
          name="NoLocationFound"
          component={NoLocationFound}
          options={{ headerShown: false, title: "NoLocationFound" }}
        />
        <Stack.Screen
          name="NoResultsFound"
          component={NoResultsFound}
          options={{ headerShown: false, title: "NoResultsFound" }}
        />
        <Stack.Screen
          name="TabNavigator"
          component={TabNavigator}
          options={{ headerShown: false, gestureEnabled: false }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default RootStack;
